<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Auth;
use App\Models\SavedItem;

class SavedItemController extends Controller
{
    /**
     * Display a listing of the saved items.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $savedItems = SavedItem::where('user_id', Auth::user()->id)
                               ->with('product')
                               ->paginate(15);
        return view('frontend.user.saved_items', compact('savedItems'));
    }

    /**
     * Store a newly created saved item in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        if(Auth::check() && Auth::user()->user_type == 'customer'){
            $savedItem = SavedItem::where('user_id', Auth::user()->id)
                                  ->where('product_id', $request->id)
                                  ->first();
            if($savedItem == null){
                $savedItem = new SavedItem;
                $savedItem->user_id = Auth::user()->id;
                $savedItem->product_id = $request->id;
                $savedItem->save();

                return response()->json([
                    'success' => true,
                    'message' => translate('Product added to saved items'),
                    'action' => 'added'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => translate('Product already in saved items'),
                    'action' => 'exists'
                ]);
            }
        }
        return response()->json([
            'success' => false,
            'message' => translate('Only customers can save items'),
            'action' => 'unauthorized'
        ]);
    }

    /**
     * Remove the specified saved item from storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function remove(Request $request)
    {
        $savedItem = SavedItem::findOrFail($request->id);
        if($savedItem != null && $savedItem->user_id == Auth::user()->id){
            if(SavedItem::destroy($request->id)){
                return response()->json([
                    'success' => true,
                    'message' => translate('Product removed from saved items'),
                    'action' => 'removed'
                ]);
            }
        }
        return response()->json([
            'success' => false,
            'message' => translate('Unable to remove item'),
            'action' => 'error'
        ]);
    }

    /**
     * Check if product is in saved items
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function checkSaved(Request $request)
    {
        if(Auth::check() && Auth::user()->user_type == 'customer'){
            $savedItem = SavedItem::where('user_id', Auth::user()->id)
                                  ->where('product_id', $request->product_id)
                                  ->first();
            return response()->json([
                'is_saved' => $savedItem != null,
                'saved_item_id' => $savedItem ? $savedItem->id : null
            ]);
        }
        return response()->json(['is_saved' => false]);
    }
}
